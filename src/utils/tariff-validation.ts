import { Tarif, CreditTarif, Ou } from "@prisma/client";

/**
 * Checks if a tariff is valid for a specific OU based on operatorId
 * @param tariff - The tariff to check (Tarif or CreditTarif)
 * @param ou - The OU to check against
 * @returns boolean - true if tariff is valid for the OU
 */
export function isTariffValidForOu(
  tariff: Tarif | CreditTarif | any,
  ou: Ou | any
): boolean {
  // If tariff has no operatorId, it's valid for all OUs
  if (!tariff.operatorId) {
    return true;
  }

  // If OU has no operatorId, tariff with operatorId is not valid
  if (!ou.operatorId) {
    return false;
  }

  // Check if operatorIds match
  return tariff.operatorId === ou.operatorId;
}

/**
 * Filters tariffs that are valid for a specific OU
 * @param tariffs - Array of tariffs to filter
 * @param ou - The OU to check against
 * @returns Array of valid tariffs
 */
export function filterTariffsForOu<T extends { operatorId?: string | null }>(
  tariffs: T[],
  ou: Ou | any
): T[] {
  return tariffs.filter(tariff => isTariffValidForOu(tariff, ou));
}

/**
 * Gets all OUs that a tariff is valid for based on operatorId
 * @param tariff - The tariff to check
 * @param ous - Array of all OUs
 * @returns Array of valid OUs
 */
export function getValidOusForTariff<T extends { operatorId?: string | null }>(
  tariff: T,
  ous: Ou[]
): Ou[] {
  // If tariff has no operatorId, it's valid for all OUs
  if (!tariff.operatorId) {
    return ous;
  }

  // Return OUs with matching operatorId
  return ous.filter(ou => ou.operatorId === tariff.operatorId);
}

/**
 * Validates if a contact can be assigned to a tariff based on their OU
 * @param contact - Contact with OU information
 * @param tariff - The tariff to check
 * @returns boolean - true if assignment is valid
 */
export function canAssignContactToTariff(
  contact: { ou?: Ou | null } | any,
  tariff: { operatorId?: string | null } | any
): boolean {
  // If contact has no OU, assignment is not valid for tariffs with operatorId
  if (!contact.ou) {
    return !tariff.operatorId;
  }

  return isTariffValidForOu(tariff, contact.ou);
}

/**
 * Gets validation message for tariff-OU compatibility
 * @param tariff - The tariff to check
 * @param ou - The OU to check against
 * @returns string - Validation message
 */
export function getTariffValidationMessage(
  tariff: { operatorId?: string | null; name?: string } | any,
  ou: { operatorId?: string | null; name?: string; code?: string } | any
): string {
  if (!tariff.operatorId) {
    return `Tariff "${tariff.name}" is valid for all OUs`;
  }

  if (!ou.operatorId) {
    return `OU "${ou.name}" (${ou.code}) has no operator ID - tariff "${tariff.name}" with operator ID "${tariff.operatorId}" is not valid`;
  }

  if (tariff.operatorId === ou.operatorId) {
    return `Tariff "${tariff.name}" is valid for OU "${ou.name}" (${ou.code}) - matching operator ID "${tariff.operatorId}"`;
  }

  return `Tariff "${tariff.name}" (operator ID: ${tariff.operatorId}) is not valid for OU "${ou.name}" (operator ID: ${ou.operatorId})`;
}
