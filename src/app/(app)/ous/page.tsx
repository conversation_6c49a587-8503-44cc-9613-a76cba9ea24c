"use client";

import Card from "../../../component/card";
import Headline from "../../../component/Headline";
import { useEffect, useState } from "react";
import Table from "~/utils/table/table";
import type { ColDef, ColGroupDef } from "ag-grid-community";
import type { ICellRendererParams } from "ag-grid-community";
import Link from "next/link";
import Button from "~/component/button";
import { HiMagnifyingGlass } from "react-icons/hi2";
import Loading from "../loading";

interface OuData {
  id: string;
  name: string;
  code: string;
  operatorId?: string | null;
  externalReference?: string | null;
  _count: {
    User: number;
    Location: number;
  };
}

const OusPage = () => {
  const [ous, setOus] = useState<OuData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchOus = async () => {
      try {
        const response = await fetch("/api/admin/ous");
        if (response.ok) {
          const data = await response.json();
          setOus(data);
        }
      } catch (error) {
        console.error("Error fetching OUs:", error);
      } finally {
        setLoading(false);
      }
    };

    void fetchOus();
  }, []);

  // OperatorId Status Renderer
  const OperatorIdRenderer = (params: ICellRendererParams) => {
    const operatorId = params.value;

    if (operatorId) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          {operatorId}
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
          Nicht gesetzt
        </span>
      );
    }
  };

  // Action Cell Renderer
  const ActionCellRenderer = (params: ICellRendererParams) => {
    return (
      <Link href={`/ous/${params.data.id}/edit`}>
        <Button className="bg-primary text-white hover:bg-primary-dark text-xs px-3 py-1 flex items-center gap-1">
          <HiMagnifyingGlass size={12} />
          Bearbeiten
        </Button>
      </Link>
    );
  };

  const columnDefs: (ColDef | ColGroupDef)[] = [
    {
      field: "name",
      headerName: "Name",
      width: 200,
      pinned: "left"
    },
    {
      field: "code",
      headerName: "Code",
      width: 150
    },
    {
      field: "operatorId",
      headerName: "Operator ID",
      width: 150,
      cellRenderer: OperatorIdRenderer,
      editable: false
    },
    {
      field: "externalReference",
      headerName: "Externe Referenz",
      width: 180,
      valueFormatter: (params) => params.value || "-"
    },
    {
      field: "_count.User",
      headerName: "Benutzer",
      width: 120,
      valueGetter: (params) => params.data._count?.User || 0
    },
    {
      field: "_count.Location",
      headerName: "Standorte",
      width: 120,
      valueGetter: (params) => params.data._count?.Location || 0
    },
    {
      field: "action",
      headerName: "Aktionen",
      width: 120,
      cellRenderer: ActionCellRenderer,
      editable: false,
      sortable: false,
      filter: false,
      pinned: "right"
    }
  ];

  if (loading) {
    return <Loading />;
  }

  return (
    <Card>
      <Headline title="Organisationseinheiten Verwaltung" />

      <div className="h-[80vh] min-h-[500px]">
        <Table
          gridId="ous"
          columnDefs={columnDefs}
          rowData={ous}
          overlayNoRowsTemplate="Keine Organisationseinheiten gefunden"
        />
      </div>
    </Card>
  );
};

export default OusPage;
