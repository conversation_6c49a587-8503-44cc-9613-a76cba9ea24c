"use client";

import { AgGridReact } from "ag-grid-react";
import React, { useState } from "react";

import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";
import type { GridOptions, ICellRendererParams } from "ag-grid-community";
import type {
  ColDef,
  ColGroupDef,
  ValueGetterParams,
} from "ag-grid-community/dist/lib/entities/colDef";
import Link from "next/link";
import { BsPencilSquare } from "react-icons/bs";
import { MdBookmarkAdd } from "react-icons/md";
import { KindOfTarif } from "@prisma/client";
import { Chip } from "~/app/(app)/component/chip";
import Table from "~/utils/table/table";

export const RoamingTariffTable = ({ data }: any) => {
  const CellRenderer = (params: ICellRendererParams) => {
    return (
      <div className="flex gap-2">
        <Link href={`tarif/roaming/update/${params.data?.id}`}>
          <BsPencilSquare className="cursor-pointer text-green-600 hover:text-green-800" />
        </Link>
        {params.data.kindOfTarif != KindOfTarif.DIRECT && (
          <Link href={`tarif/mapping/${params.data?.id}`} title={"Abonennten verwalten"}>
            <MdBookmarkAdd className="cursor-pointer text-blue-600 hover:text-blue-800" />
          </Link>
        )}
      </div>
    );
  };

  const columnDefs = [
    {
      field: "name",
      width: 400,
      pinned: "left"
    },
    {
      field: "id",
      width: 150,
      minWidth: 120,
      headerName: "Tariftyp",
      valueGetter: (params: ValueGetterParams) => params?.data?.kindOfTarif,
      cellRenderer: (params: ICellRendererParams) => {
        if (params.data?.kindOfTarif == KindOfTarif.ROAMING) {
          return <Chip label={"Roaming"} className={"bg-blue-100 text-blue-800"} />;
        } else if (params.data?.kindOfTarif == KindOfTarif.DIRECT) {
          return <Chip label={"Direct"} className={"bg-gray-100 text-gray-800"} />;
        }
        return <Chip label={"Tarif"} className={"bg-gray-100 text-gray-800"} />;
      },
    },
    { field: "sessionFee", headerName: "Preis pro Session" },
    { field: "kwh" },
    { field: "minChargingEnergy", headerName: "Mindest Lademenge in kWh" },
    { field: "currentType", headerName: "Ladepunkt-Type (AC/DC)" },
    {
      field: "validFrom",
      headerName: "Gültig ab",
      cellRenderer: (params: ICellRendererParams) =>
        `${new Date(params.data?.validFrom).toLocaleDateString("de-DE")}`,
    },
    {
      field: "validTo",
      headerName: "Gültig bis",
      cellRenderer: (params: ICellRendererParams) =>
        `${new Date(params.data?.validTo).toLocaleDateString("de-DE")}`,
    },
    {
      field: "_count",
      headerName: "Abonnierte",
      cellRenderer: (params: ICellRendererParams) => `${params.data?._count.contacts}`,
    },
    {
      field: "action",
      headerName: "Aktion",
      cellRenderer: CellRenderer,
      sortable: false,
      filter: false,
      pinned: "right",
      width: 100
    },
  ];

  return <Table gridId={"roamingtarif"} columnDefs={columnDefs} rowData={data} />;
};

export default RoamingTariffTable;
