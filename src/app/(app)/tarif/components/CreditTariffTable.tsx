"use client";
import React, { useState } from "react";
import Table from "~/utils/table/table";
import type { ICellRendererParams } from "ag-grid-community";
import Link from "next/link";
import { TfiPencil } from "react-icons/tfi";
import { MdBookmarkAdd } from "react-icons/md";

export const CreditTariffTable = ({ data }: any) => {
  const CellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        <Link href={`tarif/credit/update/${params.data.id}`}>
          <TfiPencil />
        </Link>
        <Link href={`tarif/mapping/${params.data.id}`} title={"Abonennten verwalten"}>
          <MdBookmarkAdd />
        </Link>
      </>
    );
  };

  const columnDefs = [
    {
      field: "name",
      headerName: "Name",
      width: 400,
      pinned: "left"
    },
    { field: "tarifType", headerName: "Tarif Type", width: 100 },
    { field: "powerType", headerName: "Power Type", width: 100 },
    { field: "sessionCredit", headerName: "Startgebühr", width: 400 },
    { field: "energyCredit", headerName: "kWh", width: 400 },
    { field: "blockingCredit", headerName: "Blockiergebühren" },
    { field: "maxBlockingCredit", headerName: "max Blockiergebühren" },
    { field: "blockingFeeMinStart", headerName: "Blockiergebühren ab min" },
    {
      field: "action",
      headerName: "Aktion",
      cellRenderer: CellRenderer,
      sortable: false,
      filter: false,
      pinned: "right",
      width: 100
    },
  ];
  return <Table columnDefs={columnDefs} rowData={data} />;
};
