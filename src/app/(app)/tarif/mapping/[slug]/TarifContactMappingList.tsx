"use client";
import { CreditTarif, Tarif } from "@prisma/client";
import { useRouter } from "next/navigation";
import React, { useMemo, useTransition, useState } from "react";
import type { ContactsWithIncludes } from "./page";
import Table from "~/utils/table/table";
import type { ColDef, ColGroupDef } from "ag-grid-community";
import type { ICellRendererParams } from "ag-grid-community";
import Button from "~/component/button";
import { <PERSON>Loader, FiUsers, FiUserCheck, FiSearch } from "react-icons/fi";
import { BsToggleOn, BsToggleOff } from "react-icons/bs";

interface Props {
  tarif: Tarif | CreditTarif;
  contacts: ContactsWithIncludes[];
}

const TarifContactMappingList = ({ tarif, contacts }: Props) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [searchTerm, setSearchTerm] = useState("");
  const [bulkActionLoading, setBulkActionLoading] = useState(false);

  const apiEndpoint = useMemo(() => {
    if ("sessionCredit" in tarif) {
      return "/api/contact/mapCreditTarif";
    } else {
      return "/api/contact/mapRoamingTarif";
    }
  }, [tarif]);

  const mapTarifToContact = async (contact: ContactsWithIncludes, subscribed: boolean) => {
    await fetch(apiEndpoint, {
      method: "POST",
      body: JSON.stringify({
        contact: contact,
        tarif: tarif,
        subscribed: subscribed,
      }),
    });
    startTransition(() => {
      // Refresh the current route and fetch new data from the server without
      // losing client-side browser or React state.
      router.refresh();
    });
  };

  const filteredContacts = useMemo(() => {
    let filtered;
    if ("energyCredit" in tarif) {
      filtered = contacts.filter((contact) => contact.cpo);
    } else {
      filtered = contacts.filter((contact) => !contact.cpo);
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter((contact) =>
        contact.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.customerNumber?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [contacts, tarif, searchTerm]);

  const isMapped = (contact: ContactsWithIncludes, tarif: Tarif | CreditTarif) => {
    if ("energyCredit" in tarif) {
      return !!contact.creditTarifs.find((contactTarif) => contactTarif.creditTarifId == tarif.id);
    } else {
      return !!contact.tarifs.find((contactTarif) => contactTarif.tarifId == tarif.id);
    }
  };

  // Statistics
  const mappedCount = filteredContacts.filter(contact => isMapped(contact, tarif)).length;
  const totalCount = filteredContacts.length;

  // Bulk actions
  const handleBulkAction = async (mapAll: boolean) => {
    setBulkActionLoading(true);
    try {
      const promises = filteredContacts
        .filter(contact => isMapped(contact, tarif) !== mapAll)
        .map(contact => mapTarifToContact(contact, mapAll));

      await Promise.all(promises);
    } finally {
      setBulkActionLoading(false);
    }
  };

  // Status Cell Renderer
  const StatusCellRenderer = (params: ICellRendererParams) => {
    const contact = params.data;
    const mapped = isMapped(contact, tarif);

    return (
      <div className="flex items-center justify-center">
        {mapped ? (
          <BsToggleOn
            className="cursor-pointer text-emerald-500 hover:text-emerald-600 text-xl"
            onClick={() => mapTarifToContact(contact, false)}
            title="Zugeordnet - Klicken zum Entfernen"
          />
        ) : (
          <BsToggleOff
            className="cursor-pointer text-gray-400 hover:text-gray-600 text-xl"
            onClick={() => mapTarifToContact(contact, true)}
            title="Nicht zugeordnet - Klicken zum Zuordnen"
          />
        )}
      </div>
    );
  };

  // Complete Tariff Display Component
  const TariffDisplay = () => {
    const isCredit = "energyCredit" in tarif;
    const standardTarif = tarif as any; // Type assertion for easier access

    return (
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              {standardTarif.name || "Unbenannter Tarif"}
            </h3>
            <div className="flex gap-2 mt-2">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                isCredit
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                  : 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200'
              }`}>
                {isCredit ? 'Credit Tarif' : 'Standard Tarif'}
              </span>
              {standardTarif.kindOfTarif && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                  {standardTarif.kindOfTarif}
                </span>
              )}
              {standardTarif.operatorId && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                  Operator: {standardTarif.operatorId}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Pricing Section */}
        <div className="mb-6">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Preise</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {isCredit ? (
              <>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Session Credit</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {(standardTarif.sessionCredit || 0).toFixed(2)} €
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">netto</p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Energy Credit</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {(standardTarif.energyCredit || 0).toFixed(2)} €/kWh
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">netto</p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Blocking Credit</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {(standardTarif.blockingCredit || 0).toFixed(2)} €/min
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    netto, max. {(standardTarif.maxBlockingCredit || 0).toFixed(2)} €
                  </p>
                </div>
              </>
            ) : (
              <>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Session Fee</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {(standardTarif.sessionFee || 0).toFixed(2)} €
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">netto</p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">kWh Price</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {(standardTarif.kwh || 0).toFixed(2)} €/kWh
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">netto</p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Blocking Fee</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {(standardTarif.blockingFee || 0).toFixed(2)} €/min
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    netto, ab {standardTarif.blockingFeeBeginAtMin || 0} min, max. {(standardTarif.blockingFeeMax || 0).toFixed(2)} €
                  </p>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Technical Details Section */}
        <div className="mb-6">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Technische Details</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {standardTarif.currentType && (
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Ladepunkt-Typ</p>
                <p className="text-base font-semibold text-gray-900 dark:text-white">
                  {standardTarif.currentType}
                </p>
              </div>
            )}

            {!isCredit && (
              <>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Min. Ladezeit</p>
                  <p className="text-base font-semibold text-gray-900 dark:text-white">
                    {(standardTarif.minChargingTime || 0).toFixed(1)} min
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Min. Lademenge</p>
                  <p className="text-base font-semibold text-gray-900 dark:text-white">
                    {(standardTarif.minChargingEnergy || 0).toFixed(1)} kWh
                  </p>
                </div>
              </>
            )}

            {isCredit && standardTarif.tarifType && (
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Tarif Typ</p>
                <p className="text-base font-semibold text-gray-900 dark:text-white">
                  {standardTarif.tarifType}
                </p>
              </div>
            )}

            {isCredit && standardTarif.powerType && (
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Power Typ</p>
                <p className="text-base font-semibold text-gray-900 dark:text-white">
                  {standardTarif.powerType}
                </p>
              </div>
            )}

            {isCredit && standardTarif.blockingFeeMinStart && (
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Blocking ab</p>
                <p className="text-base font-semibold text-gray-900 dark:text-white">
                  {standardTarif.blockingFeeMinStart} min
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Validity Section */}
        <div className="mb-6">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Gültigkeit</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {standardTarif.validFrom && (
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Gültig ab</p>
                <p className="text-base font-semibold text-gray-900 dark:text-white">
                  {new Date(standardTarif.validFrom).toLocaleDateString('de-DE')}
                </p>
              </div>
            )}
            {standardTarif.validTo && (
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Gültig bis</p>
                <p className="text-base font-semibold text-gray-900 dark:text-white">
                  {new Date(standardTarif.validTo).toLocaleDateString('de-DE')}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Additional Information */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Tarif ID:</span>
              <span className="ml-2 text-gray-900 dark:text-white font-mono text-xs">
                {standardTarif.id}
              </span>
            </div>

            {standardTarif.contractId && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Contract ID:</span>
                <span className="ml-2 text-gray-900 dark:text-white font-mono text-xs">
                  {standardTarif.contractId}
                </span>
              </div>
            )}

            {standardTarif._count && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Zugeordnete Kontakte:</span>
                <span className="ml-2 text-gray-900 dark:text-white">
                  {standardTarif._count.contacts || 0}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const columnDefs: (ColDef | ColGroupDef)[] = [
    {
      field: "name",
      headerName: "Name",
      flex: 2,
      pinned: "left"
    },
    {
      field: "companyName",
      headerName: "Unternehmen",
      flex: 2,
      valueFormatter: (params) => params.value || "-"
    },
    {
      field: "customerNumber",
      headerName: "Kundennummer",
      flex: 1,
      valueFormatter: (params) => params.value || "-"
    },
    {
      field: "status",
      headerName: "Status",
      width: 100,
      cellRenderer: StatusCellRenderer,
      sortable: false,
      filter: false,
      pinned: "right"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Tariff Display */}
      <TariffDisplay />

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <FiUsers className="text-blue-500 mr-3" size={20} />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Verfügbare Kontakte</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <FiUserCheck className="text-emerald-500 mr-3" size={20} />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Zugeordnet</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{mappedCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <div className="w-5 h-5 bg-gray-300 rounded mr-3"></div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Nicht zugeordnet</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalCount - mappedCount}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Bulk Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <input
            type="text"
            placeholder="Kontakte durchsuchen..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:shadow-soft-primary-outline focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div className="flex gap-2">
          <Button
            onClick={() => handleBulkAction(true)}
            disabled={bulkActionLoading || mappedCount === totalCount}
            className="bg-emerald-500 hover:bg-emerald-600 text-white text-sm px-3 py-2"
          >
            {bulkActionLoading ? <FiLoader className="animate-spin mr-1" /> : null}
            Alle zuordnen
          </Button>
          <Button
            onClick={() => handleBulkAction(false)}
            disabled={bulkActionLoading || mappedCount === 0}
            className="bg-slate-500 hover:bg-slate-600 text-white text-sm px-3 py-2"
          >
            {bulkActionLoading ? <FiLoader className="animate-spin mr-1" /> : null}
            Alle entfernen
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="h-[60vh] min-h-[400px]">
        <Table
          gridId="tarif-contact-mapping"
          columnDefs={columnDefs}
          rowData={filteredContacts}
          overlayNoRowsTemplate={
            searchTerm
              ? `Keine Kontakte gefunden für "${searchTerm}"`
              : "Keine Kontakte verfügbar"
          }
        />
      </div>
    </div>
  );
};

export default TarifContactMappingList;
